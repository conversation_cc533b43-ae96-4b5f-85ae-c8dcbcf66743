import { useEffect, useRef, useCallback } from 'react'
import { logout } from '@/hooks/useAuth'
import { Auth } from '@/services/client/cognito'
import { jwtDecode } from 'jwt-decode'
import SESSION_CONFIG from '@/constants/sessionConfig'

const useSessionTimeout = () => {
  const inactivityTimeoutRef = useRef(null)
  const tokenExpiryTimeoutRef = useRef(null)
  const lastActivityRef = useRef(Date.now())

  // Use configurable timeout values
  const {
    INACTIVITY_TIMEOUT_MS,
    INACTIVITY_TIMEOUT_MINUTES,
    TOKEN_REFRESH_BUFFER_MS,
    ENABLE_DEBUG_LOGGING
  } = SESSION_CONFIG

  const clearAllTimeouts = useCallback(() => {
    if (inactivityTimeoutRef.current) {
      clearTimeout(inactivityTimeoutRef.current)
      inactivityTimeoutRef.current = null
    }
    if (tokenExpiryTimeoutRef.current) {
      clearTimeout(tokenExpiryTimeoutRef.current)
      tokenExpiryTimeoutRef.current = null
    }
  }, [])

  const setupTokenExpiryTimeout = useCallback(async () => {
    try {
      const session = await Auth.fetchAuthSession()
      const accessToken = session.tokens?.accessToken?.toString()
      
      if (accessToken) {
        const decoded = jwtDecode(accessToken)
        const tokenExpiryTime = decoded.exp * 1000 // Convert to milliseconds
        const currentTime = Date.now()
        const timeUntilExpiry = tokenExpiryTime - currentTime
        
        // Only set timeout if token hasn't expired yet
        if (timeUntilExpiry > 0) {
          tokenExpiryTimeoutRef.current = setTimeout(async () => {
            // Check if user is still active before logging out
            const timeSinceLastActivity = Date.now() - lastActivityRef.current
            const isUserActive = timeSinceLastActivity < INACTIVITY_TIMEOUT_MS

            if (isUserActive) {
              if (ENABLE_DEBUG_LOGGING) {
                console.log('Token expired but user is active, refreshing token')
              }
              // Let AWS Amplify handle token refresh automatically
              // Just reset the timeout for the new token
              setupTokenExpiryTimeout()
            } else {
              if (ENABLE_DEBUG_LOGGING) {
                console.log('Token expired and user inactive, logging out')
              }
              logout()
            }
          }, timeUntilExpiry)

          if (ENABLE_DEBUG_LOGGING) {
            console.log(`Token expires in ${Math.round(timeUntilExpiry / 60000)} minutes`)
          }
        }
      }
    } catch (error) {
      if (ENABLE_DEBUG_LOGGING) {
        console.error('Error setting up token expiry timeout:', error)
      }
    }
  }, [])

  const resetInactivityTimeout = useCallback(() => {
    lastActivityRef.current = Date.now()
    
    // Clear existing inactivity timeout
    if (inactivityTimeoutRef.current) {
      clearTimeout(inactivityTimeoutRef.current)
    }
    
    // Set new inactivity timeout - only for inactive users
    inactivityTimeoutRef.current = setTimeout(() => {
      // Double-check if user is still inactive before logging out
      // This prevents race conditions when user becomes active just before timeout
      const timeSinceLastActivity = Date.now() - lastActivityRef.current
      const isStillInactive = timeSinceLastActivity >= INACTIVITY_TIMEOUT_MS

      if (isStillInactive) {
        if (ENABLE_DEBUG_LOGGING) {
          console.log(`User inactive for ${INACTIVITY_TIMEOUT_MINUTES} minutes, logging out`)
        }
        logout()
      } else {
        if (ENABLE_DEBUG_LOGGING) {
          console.log('User became active before timeout, resetting timer')
        }
        // User became active, reset the timeout
        resetInactivityTimeout()
      }
    }, INACTIVITY_TIMEOUT_MS)
  }, [])

  const resetTimeouts = useCallback(() => {
    // Reset inactivity timeout on user activity
    resetInactivityTimeout()
    
    // Reset token expiry timeout (this will get a fresh token if needed)
    setupTokenExpiryTimeout()
  }, [setupTokenExpiryTimeout, resetInactivityTimeout])

  useEffect(() => {
    // Initial setup
    if (ENABLE_DEBUG_LOGGING) {
      console.log('Session timeout initialized', {
        inactivityTimeoutMinutes: INACTIVITY_TIMEOUT_MINUTES,
        maxSessionAgeMinutes: SESSION_CONFIG.MAX_SESSION_AGE_MINUTES,
        tokenRefreshBufferMinutes: SESSION_CONFIG.TOKEN_REFRESH_BUFFER_MINUTES
      })
    }

    setupTokenExpiryTimeout()
    resetInactivityTimeout()

    // Track user activity events
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click']

    events.forEach(event => {
      document.addEventListener(event, resetTimeouts, true)
    })

    // Cleanup
    return () => {
      events.forEach(event => {
        document.removeEventListener(event, resetTimeouts, true)
      })
      clearAllTimeouts()
    }
  }, [setupTokenExpiryTimeout, resetInactivityTimeout, resetTimeouts, clearAllTimeouts])

  // Expose reset function globally for axios interceptor
  useEffect(() => {
    window.resetSessionTimeouts = resetTimeouts
    
    return () => {
      delete window.resetSessionTimeouts
    }
  }, [resetTimeouts])

  return { resetTimeouts }
}

export default useSessionTimeout
